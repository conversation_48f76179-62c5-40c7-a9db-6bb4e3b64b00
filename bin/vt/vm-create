#!/bin/bash

set -euo pipefail

DOT_DIR=${DOT_DIR:-$HOME/.ilm}
# shellcheck disable=SC1091
source "$DOT_DIR/share/utils"

VCPUS=4
RAM_MB=4096
DISK_SIZE="40G"
BRIDGE_IF="virbr0"
INSTALL_DOCKER=false
INSTALL_BREW=false
DOTFILES_OPTIONS=""
INSTALL_NIX=""

unset VM_NAME
unset USERNAME
unset RELEASE
unset DISTRO
unset PASSWORD

if [[ -f "${HOME}/.ssh/id_ed25519.pub" ]]; then
    SSH_KEY="${HOME}/.ssh/id_ed25519.pub"
elif [[ -f "${HOME}/.ssh/id_rsa.pub" ]]; then
    SSH_KEY="${HOME}/.ssh/id_rsa.pub"
else
    ssh-keygen -t rsa -b 4096 -f "${HOME}/.ssh/id_rsa" -N ""
    SSH_KEY="${HOME}/.ssh/id_rsa.pub"
fi

SUDO_KEEPALIVE_PID=""

start_sudo_keepalive() {
    slog "Starting sudo keepalive process..."
    sudo -v
    (while true; do
        sudo -v
        sleep 50
    done) &
    SUDO_KEEPALIVE_PID=$!
}

stop_sudo_keepalive() {
    if [ -n "$SUDO_KEEPALIVE_PID" ] && kill -0 "$SUDO_KEEPALIVE_PID" 2>/dev/null; then
        slog "Stopping sudo keepalive process..."
        kill "$SUDO_KEEPALIVE_PID" 2>/dev/null
    fi
    SUDO_KEEPALIVE_PID=""
}

cleanup_on_error() {
    warn "Cleaning up due to error..."
    sudo rm -f "$DISK_IMG" 2>/dev/null || true
    sudo rm -f "$SEED_ISO" 2>/dev/null || true
    sudo virsh destroy "$VM_NAME" 2>/dev/null || true
    sudo virsh undefine "$VM_NAME" 2>/dev/null || true
    stop_sudo_keepalive
}

trap cleanup_on_error ERR
trap 'cleanup_on_error; exit 1' INT TERM

configure_ubuntu() {
    VM_NAME=${VM_NAME:=ubuntu}
    USERNAME=${USERNAME:=$VM_NAME}
    RELEASE=${RELEASE:=noble} # 24.04 LTS

    IMG_URL="https://cloud-images.ubuntu.com/${RELEASE}/current/${RELEASE}-server-cloudimg-amd64.img"
    BASE_IMG_NAME="${RELEASE}.img"
    USER_GROUPS=("adm" "sudo")
    INSTALL_CMD="sudo apt install qemu-kvm libvirt-daemon-system libvirt-clients virtinst qemu-utils wget genisoimage"

    case "$RELEASE" in
    "noble") OS_VARIANT="ubuntu24.04" ;;
    "jammy") OS_VARIANT="ubuntu22.04" ;;
    "focal") OS_VARIANT="ubuntu20.04" ;;
    *)
        warn "Unknown Ubuntu release '$RELEASE', using ubuntu24.04 as os-variant"
        OS_VARIANT="ubuntu24.04"
        ;;
    esac
}

configure_fedora() {
    VM_NAME=${VM_NAME:=fedora}
    USERNAME=${USERNAME:=$VM_NAME}
    RELEASE=${RELEASE:=42}

    if [[ "$RELEASE" -ge 42 ]]; then
        IMG_URL="https://download.fedoraproject.org/pub/fedora/linux/releases/${RELEASE}/Cloud/x86_64/images/Fedora-Cloud-Base-Generic-${RELEASE}-1.1.x86_64.qcow2"
    else
        IMG_URL="https://download.fedoraproject.org/pub/fedora/linux/releases/${RELEASE}/Cloud/x86_64/images/Fedora-Cloud-Base-${RELEASE}-1.14.x86_64.qcow2"
    fi

    BASE_IMG_NAME="fedora-${RELEASE}.qcow2"
    USER_GROUPS=("wheel")
    INSTALL_CMD="sudo dnf install qemu-kvm libvirt virt-install qemu-img wget genisoimage"
    OS_VARIANT="fedora${RELEASE}"

    # Get the osinfo list once and reuse it
    local osinfo_list
    osinfo_list=$(virt-install --osinfo list 2>/dev/null)

    if ! echo "$osinfo_list" | grep -q "fedora${RELEASE}"; then
        warn "OS variant 'fedora${RELEASE}' not found in virt-install osinfo list, using 'fedora41'"
        slog "Available fedora variants:"
        echo "$osinfo_list" | grep -i fedora | head -5 || true
        OS_VARIANT="fedora41"
    fi
}

configure_arch() {
    VM_NAME=${VM_NAME:=arch}
    USERNAME=${USERNAME:=$VM_NAME}
    RELEASE=${RELEASE:=latest}

    IMG_URL="https://geo.mirror.pkgbuild.com/images/latest/Arch-Linux-x86_64-cloudimg.qcow2"
    BASE_IMG_NAME="arch-latest.qcow2"
    USER_GROUPS=("wheel")
    INSTALL_CMD="sudo pacman -S qemu-desktop libvirt virt-install qemu-img wget cdrtools"
    OS_VARIANT="archlinux"

    # Get the osinfo list once and reuse it
    local osinfo_list
    osinfo_list=$(virt-install --osinfo list 2>/dev/null)

    if ! echo "$osinfo_list" | grep -q "archlinux"; then
        warn "OS variant 'archlinux' not found in virt-install osinfo list, using 'linux'"
        slog "Available arch-related variants:"
        echo "$osinfo_list" | grep -i arch || true
        OS_VARIANT="linux"
    fi
}

configure_debian() {
    VM_NAME=${VM_NAME:=debian}
    USERNAME=${USERNAME:=$VM_NAME}
    RELEASE=${RELEASE:=bookworm} # 12

    case "$RELEASE" in
    "bookworm")
        IMG_URL="https://cloud.debian.org/images/cloud/bookworm/latest/debian-12-generic-amd64.qcow2"
        OS_VARIANT="debian12"
        ;;
    "bullseye")
        IMG_URL="https://cloud.debian.org/images/cloud/bullseye/latest/debian-11-generic-amd64.qcow2"
        OS_VARIANT="debian11"
        ;;
    "trixie")
        IMG_URL="https://cloud.debian.org/images/cloud/trixie/latest/debian-13-generic-amd64.qcow2"
        OS_VARIANT="debian12" # Use debian12 for now until trixie is recognized
        ;;
    *)
        fail "Unsupported Debian release: $RELEASE"
        exit 1
        ;;
    esac

    BASE_IMG_NAME="debian-${RELEASE}.qcow2"
    USER_GROUPS=("sudo")
    INSTALL_CMD="sudo apt install qemu-kvm libvirt-daemon-system libvirt-clients virtinst qemu-utils wget genisoimage"
}

configure_alpine() {
    VM_NAME=${VM_NAME:=alpine}
    USERNAME=${USERNAME:=alpine} # Alpine Linux uses 'alpine' as the default user
    RELEASE=${RELEASE:=3.22}     # Current stable version

    case "$RELEASE" in
    "3.22")
        IMG_URL="https://dl-cdn.alpinelinux.org/alpine/v3.22/releases/cloud/nocloud_alpine-3.22.0-x86_64-bios-cloudinit-r0.qcow2"
        OS_VARIANT="alpinelinux3.21" # Use 3.21 as closest available variant
        ;;
    "3.21")
        IMG_URL="https://dl-cdn.alpinelinux.org/alpine/v3.21/releases/cloud/nocloud_alpine-3.21.3-x86_64-bios-cloudinit-r0.qcow2"
        OS_VARIANT="alpinelinux3.21"
        ;;
    "3.20")
        IMG_URL="https://dl-cdn.alpinelinux.org/alpine/v3.20/releases/cloud/nocloud_alpine-3.20.6-x86_64-bios-cloudinit-r0.qcow2"
        OS_VARIANT="alpinelinux3.20"
        ;;
    *)
        warn "Unknown Alpine release '$RELEASE', using 3.22.0"
        IMG_URL="https://dl-cdn.alpinelinux.org/alpine/v3.22/releases/cloud/nocloud_alpine-3.22.0-x86_64-bios-cloudinit-r0.qcow2"
        OS_VARIANT="alpinelinux3.21" # Use 3.21 as closest available variant
        RELEASE="3.22"
        ;;
    esac

    BASE_IMG_NAME="alpine-${RELEASE}.qcow2"
    USER_GROUPS=("wheel")
    INSTALL_CMD="sudo apk add qemu-system-x86_64 libvirt-daemon qemu-img wget cdrkit"

    # Get the osinfo list once and reuse it
    local osinfo_list
    osinfo_list=$(virt-install --osinfo list 2>/dev/null)

    # Verify the selected OS variant is actually available
    if ! echo "$osinfo_list" | grep -q "^${OS_VARIANT}$"; then
        warn "OS variant '${OS_VARIANT}' not found in virt-install osinfo list"
        slog "Available alpine-related variants:"
        echo "$osinfo_list" | grep -i alpine | head -5 || true

        # Try to find the best available Alpine variant
        if echo "$osinfo_list" | grep -q "alpinelinux3.21"; then
            OS_VARIANT="alpinelinux3.21"
            slog "Using alpinelinux3.21 as fallback"
        elif echo "$osinfo_list" | grep -q "alpinelinux3.20"; then
            OS_VARIANT="alpinelinux3.20"
            slog "Using alpinelinux3.20 as fallback"
        else
            OS_VARIANT="linux"
            slog "Using generic 'linux' as fallback"
        fi
    fi
}

configure_distribution() {
    case "$DISTRO" in
    ubuntu) configure_ubuntu ;;
    fedora) configure_fedora ;;
    arch) configure_arch ;;
    debian) configure_debian ;;
    alpine) configure_alpine ;;
    *)
        fail "Unsupported distribution: $DISTRO"
        fail "Supported distributions: ubuntu, fedora, arch, debian, alpine"
        exit 1
        ;;
    esac

    if [[ -z "$VM_NAME" ]]; then
        fail "VM name is empty after configuration"
        exit 1
    fi

    WORKDIR="/var/lib/libvirt/images/${VM_NAME}-vm"
    CLOUD_INIT_DIR="${WORKDIR}/cloud-init"
    DISK_IMG="${WORKDIR}/${VM_NAME}.qcow2"
    SEED_ISO="${CLOUD_INIT_DIR}/seed.iso"
    BASE_IMG="${WORKDIR}/${BASE_IMG_NAME}"
    PASSWORD=${PASSWORD:=$USERNAME}

    PASSWORD_HASH=$(openssl passwd -6 "$PASSWORD")
}

usage() {
    cat <<EOF
Usage: $0 --distro DISTRO [OPTIONS]

Create a VM using virt-install and cloud-init for various Linux distributions.

REQUIRED:
    --distro DISTRO     Distribution to install (ubuntu|fedora|arch|debian|alpine)

OPTIONS:
    --name NAME         VM name (default: distribution name)
    --memory MB         RAM in MB (default: $RAM_MB)
    --vcpus NUM         Number of vCPUs (default: $VCPUS)
    --disk-size SIZE    Disk size (default: $DISK_SIZE)
    --ssh-key PATH      SSH public key path (default: $SSH_KEY)
    --bridge BRIDGE     Network bridge (default: $BRIDGE_IF)
    --username USER     VM username (default: distribution-specific)
    --release REL       Distribution release (default: distribution-specific)
    --docker            Install Docker in the VM
    --brew              Install Homebrew and essential development tools
    --nix               Install Nix using Determinate Systems installer
    --dotfiles OPTIONS...   Install dotfiles with specified options (must be last)
    --password PASS     Set password for VM user (default: same as VM name)
    --help, -h          Show this help

DISTRIBUTION-SPECIFIC OPTIONS:
    Ubuntu:
        --release noble|jammy|focal    (default: noble - 24.04 LTS)
    Fedora:
        --release 40|41|42             (default: 42)
    Arch:
        --release latest               (default: latest)
    Debian:
        --release bookworm|bullseye|trixie  (default: bookworm - 12)
    Alpine:
        --release 3.20|3.21|3.22       (default: 3.22)

EXAMPLES:
    $0 --distro ubuntu                           # Create Ubuntu VM with defaults
    $0 --distro fedora --name myvm --memory 8192 # Custom Fedora VM
    $0 --distro arch --disk-size 80G --vcpus 4   # Arch VM with larger disk
    $0 --distro debian --release bullseye        # Debian 11 VM
    $0 --distro alpine --release 3.21            # Alpine Linux 3.21 VM
    $0 --distro ubuntu --docker                  # Ubuntu VM with Docker pre-installed
    $0 --distro ubuntu --brew                    # Ubuntu VM with Homebrew and dev tools
    $0 --distro ubuntu --nix                     # Ubuntu VM with Nix package manager
    $0 --distro fedora --docker --brew           # Fedora VM with both Docker and Homebrew
    $0 --distro alpine --name alpine-dev --memory 2048  # Lightweight Alpine VM
    $0 --distro ubuntu --name ubuntu-dev --vcpus 4 --memory 8192 --dotfiles slim-shell docker code-server

PREREQUISITES:
    - KVM/QEMU installed and running
    - libvirt daemon running
    - virt-install package installed
    - SSH key pair generated
    - Network bridge configured (or use default virbr0)

INSTALL COMMANDS BY DISTRIBUTION:
    Ubuntu/Debian: sudo apt install qemu-kvm libvirt-daemon-system libvirt-clients virtinst qemu-utils wget genisoimage
    Fedora:        sudo dnf install qemu-kvm libvirt virt-install qemu-img wget genisoimage
    Arch:          sudo pacman -S qemu-desktop libvirt virt-install qemu-img wget cdrtools
    Alpine:        sudo apk add qemu-system-x86_64 libvirt-daemon qemu-img wget cdrkit

EOF
}

while [[ $# -gt 0 ]]; do
    case $1 in
    --distro)
        DISTRO="$2"
        shift 2
        ;;
    --name)
        VM_NAME="$2"
        shift 2
        ;;
    --memory)
        RAM_MB="$2"
        shift 2
        ;;
    --vcpus)
        VCPUS="$2"
        shift 2
        ;;
    --disk-size)
        DISK_SIZE="$2"
        shift 2
        ;;
    --ssh-key)
        SSH_KEY="$2"
        shift 2
        ;;
    --bridge)
        BRIDGE_IF="$2"
        shift 2
        ;;
    --username)
        USERNAME="$2"
        shift 2
        ;;
    --release)
        RELEASE="$2"
        shift 2
        ;;
    --docker)
        INSTALL_DOCKER=true
        shift
        ;;
    --brew)
        INSTALL_BREW=true
        shift
        ;;
    --nix)
        INSTALL_NIX=true
        shift
        ;;
    --dotfiles)
        shift                 # Remove --dotfiles from arguments
        DOTFILES_OPTIONS="$*" # Capture all remaining arguments
        break                 # Exit the loop since --dotfiles must be last
        ;;
    --password)
        shift
        PASSWORD="$1"
        ;;
    --help | -h)
        usage
        exit 0
        ;;
    *)
        fail "Unknown option: $1"
        usage
        exit 1
        ;;
    esac
done

if [[ -z "$DISTRO" ]]; then
    fail "Distribution is required. Use --distro ubuntu|fedora|arch|debian|alpine"
    usage
    exit 1
fi

check_prerequisites() {
    slog "Checking prerequisites..."

    if ! sudo -v; then
        fail "This script requires sudo privileges to create files in system directories"
        exit 1
    fi

    if getent passwd libvirt-qemu >/dev/null; then
        QEMU_USER="libvirt-qemu"
        QEMU_GROUP="libvirt-qemu"
    elif getent passwd qemu >/dev/null; then
        QEMU_USER="qemu"
        QEMU_GROUP="qemu"
    elif getent passwd libvirt >/dev/null; then
        QEMU_USER="libvirt"
        QEMU_GROUP="libvirt"
    else
        warn "Could not determine QEMU user, using 'root:kvm' as fallback"
        QEMU_USER="root"
        QEMU_GROUP="kvm"
    fi

    slog "Using QEMU user/group: $QEMU_USER:$QEMU_GROUP"

    local missing_tools=()
    for tool in virsh virt-install qemu-img wget mkisofs; do
        if ! has_cmd "$tool"; then
            missing_tools+=("$tool")
        fi
    done

    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        fail "Missing required tools: ${missing_tools[*]}"
        fail "Install with: $INSTALL_CMD"
        exit 1
    fi

    if ! systemctl is-active --quiet libvirtd; then
        fail "libvirtd service is not running"
        fail "Start with: sudo systemctl start libvirtd"
        exit 1
    fi

    if ! groups | grep -q libvirt; then
        warn "User not in libvirt group. You may need sudo for virsh commands"
        warn "Add user to group: sudo usermod -a -G libvirt \$USER"
    fi

    if [[ ! -f "$SSH_KEY" ]]; then
        fail "SSH public key not found at: $SSH_KEY"
        fail "Generate with: ssh-keygen -t rsa -C '<EMAIL>'"
        exit 1
    fi

    if virsh list --all | grep -q "$VM_NAME"; then
        fail "VM '$VM_NAME' already exists"
        fail "Remove with: virsh destroy $VM_NAME && virsh undefine $VM_NAME"
        exit 1
    fi

    if ! ip link show "$BRIDGE_IF" &>/dev/null; then
        warn "Bridge '$BRIDGE_IF' not found, will use default libvirt network"
        BRIDGE_IF="default"
    fi

    success "Prerequisites check passed"
}

download_image() {
    slog "Preparing $DISTRO cloud image..."

    if [[ -f "$BASE_IMG" ]]; then
        slog "Using existing $DISTRO image: $BASE_IMG"
        return 0
    fi

    slog "Downloading $DISTRO $RELEASE cloud image..."
    slog "URL: $IMG_URL"

    sudo mkdir -p "$WORKDIR"
    if [[ ! -d "$WORKDIR" ]]; then
        fail "Failed to create working directory: $WORKDIR"
        exit 1
    fi

    sudo chown "$QEMU_USER:$QEMU_GROUP" "$WORKDIR"
    sudo chmod 755 "$WORKDIR"

    if ! sudo wget -O "$BASE_IMG" "$IMG_URL"; then
        fail "Failed to download $DISTRO cloud image"
        sudo rm -f "$BASE_IMG"
        exit 1
    fi

    if [[ ! -f "$BASE_IMG" ]]; then
        fail "Image file was not created: $BASE_IMG"
        exit 1
    fi

    sudo chown "$QEMU_USER:$QEMU_GROUP" "$BASE_IMG"
    sudo chmod 644 "$BASE_IMG"
    success "$DISTRO cloud image downloaded: $BASE_IMG"
}

create_vm_disk() {
    slog "Creating VM disk..."

    if ! sudo cp "$BASE_IMG" "$DISK_IMG"; then
        fail "Failed to copy base image"
        exit 1
    fi

    if ! sudo qemu-img resize "$DISK_IMG" "$DISK_SIZE"; then
        fail "Failed to resize VM disk"
        exit 1
    fi

    sudo chown "$QEMU_USER:$QEMU_GROUP" "$DISK_IMG"
    sudo chmod 644 "$DISK_IMG"
    success "VM disk created: $DISK_IMG ($DISK_SIZE)"
}

generate_cloud_init() {
    slog "Generating cloud-init configuration..."

    sudo mkdir -p "$CLOUD_INIT_DIR"
    sudo chown "$QEMU_USER:$QEMU_GROUP" "$CLOUD_INIT_DIR"
    sudo chmod 755 "$CLOUD_INIT_DIR"

    if [[ ! -f "$SSH_KEY" ]]; then
        fail "SSH public key not found at: $SSH_KEY"
        exit 1
    fi

    local pub_key
    pub_key=$(cat "$SSH_KEY")

    local motd_content
    motd_content="
      Welcome to your $DISTRO $RELEASE VM ($VM_NAME)!
      Created on $(date)
      --------------------------------------------
      * Documentation: https://github.com/pervezfunctor/dotfiles
      * Support: https://github.com/pervezfunctor/dotfiles/issues
      --------------------------------------------
"

    local -a packages=(
        "qemu-guest-agent"
        "git"
        "tree"
        "curl"
        "wget"
        "unzip"
        "htop"
    )

    local -a runcmd=(
        "touch /home/<USER>/vm-setup-complete"
        "chown $USERNAME:$USERNAME /home/<USER>/vm-setup-complete"
        "chown -R $USERNAME:$USERNAME /home/<USER>"
    )

    case "$DISTRO" in
    ubuntu | debian)
        packages+=(
            "ca-certificates"
            "gnupg"
            "lsb-release"
        )

        ;;
    fedora)
        packages+=(
            "stow"
            "dnf-plugins-core"
            "ca-certificates"
            "policycoreutils-python-utils"
        )
        runcmd+=(
            "systemctl enable --now firewalld || true"
            "firewall-cmd --permanent --add-service=ssh || true"
            "firewall-cmd --reload || true"
        )
        ;;

    arch)
        packages+=(
            "openssh"
            "stow"
            "base-devel"
        )
        ;;

    alpine)
        # Alpine-specific packages with correct names
        packages+=(
            "openssh-server"
            "ca-certificates"
            "sudo"
            "bash"
            "nano"
            "qemu-guest-agent"
            "trash-cli"
            "micro"
        )
        # Alpine uses OpenRC instead of systemd
        runcmd+=(
            # Enable and start services with OpenRC
            "rc-update add sshd default"
            "rc-service sshd start"
            "rc-update add qemu-guest-agent default"
            "rc-service qemu-guest-agent start"
        )
        ;;
    esac

    if [[ "$INSTALL_DOCKER" == "true" ]]; then
        motd_content+="
      Docker is installed and ready to use.
      Quick commands:
      docker --version
      docker-compose --version
      docker run hello-world
"

        slog "Adding Docker installation to cloud-init"

        case "$DISTRO" in
        ubuntu | debian)
            runcmd+=(
                "curl -fsSL https://get.docker.com -o get-docker.sh"
                "sh get-docker.sh"
                "rm get-docker.sh"
            )
            ;;

        fedora)
            runcmd+=(
                "dnf config-manager addrepo --from-repofile=https://download.docker.com/linux/fedora/docker-ce.repo"
                "dnf -y install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin"
            )
            ;;

        arch)
            packages+=(
                "docker"
                "docker-compose"
            )
            ;;

        alpine)
            packages+=(
                "docker"
                "docker-compose"
            )
            ;;
        esac

        runcmd+=(
            "usermod -aG docker $USERNAME"
        )

        # Handle service management based on init system
        case "$DISTRO" in
        alpine)
            runcmd+=(
                "rc-update add docker default"
                "rc-service docker start"
            )
            ;;
        *)
            runcmd+=(
                "systemctl enable --now docker"
            )
            ;;
        esac
    fi

    if [[ "$INSTALL_BREW" == "true" ]]; then
        motd_content+="
      Homebrew is installed and ready to use.
      Essential development tools are pre-installed:
      git, curl, wget, vim, htop, starship, fzf, ripgrep, etc.
      Quick commands:
      brew --version
      brew list
      brew search <package>
      brew install <package>
"

        slog "Adding Homebrew installation to cloud-init"

        case "$DISTRO" in
        ubuntu | debian)
            packages+=(
                "build-essential"
                "procps"
                "file"
            )
            ;;

        fedora)
            packages+=(
                "gcc"
                "gcc-c++"
                "make"
                "procps-ng"
                "file"
            )
            ;;

        arch)
            packages+=(
                "procps-ng"
                "file"
            )
            ;;

        alpine)
            packages+=(
                "build-base"
                "procps"
                "file"
                "linux-headers"
            )
            ;;
        esac

        runcmd+=(
            "su - $USERNAME -c 'NONINTERACTIVE=1 /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"'"
            "echo 'eval \"\$(/home/<USER>/.linuxbrew/bin/brew shellenv)\"' >> /home/<USER>/.bashrc"
            "echo 'eval \"\$(/home/<USER>/.linuxbrew/bin/brew shellenv)\"' >> /home/<USER>/.profile"
            "su - $USERNAME -c 'eval \"\$(/home/<USER>/.linuxbrew/bin/brew shellenv)\" && brew install stow git curl wget vim htop starship fzf ripgrep eza zoxide fd bat'"
            "su - $USERNAME -c 'eval \"\$(/home/<USER>/.linuxbrew/bin/brew shellenv)\" && brew install gum tmux lazygit git-delta just jq yq'"
        )
    fi

    if [[ "$INSTALL_NIX" == "true" ]]; then
        motd_content+="
      Nix package manager is installed and ready to use.
      Quick commands:
        nix --version
        nix search <package>
        nix-env -iA nixpkgs.<package>
        nix-shell -p <package>
"

        slog "Adding Nix installation to cloud-init"

        packages+=(
            "curl"
            "ca-certificates"
        )

        runcmd+=(
            "curl --proto '=https' --tlsv1.2 -sSf -L https://install.determinate.systems/nix | sh -s -- install --no-confirm"
            "echo 'source /nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh' >> /etc/bash.bashrc"
            "echo 'source /nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh' >> /etc/profile"
            "echo 'source /nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh' >> /home/<USER>/.bashrc"
        )

        # Handle Nix daemon based on init system
        case "$DISTRO" in
        alpine)
            runcmd+=(
                "rc-update add nix-daemon default || true"
                "rc-service nix-daemon start || true"
            )
            ;;
        *)
            runcmd+=(
                "systemctl enable --now nix-daemon || true"
            )
            ;;
        esac
    fi

    if [[ -n "$DOTFILES_OPTIONS" ]]; then
        motd_content+="
      Dotfiles are installed and ready to use.
      Location: /home/<USER>/.ilm
      Quick commands:
        ilmi shell
        ilmi docker
        ilmi python
        ilmc zsh
        ilmc tmux
        ilmc nvim
"

        slog "Adding dotfiles installation to cloud-init with options: $DOTFILES_OPTIONS"

        runcmd+=(
            "su - $USERNAME -c 'bash -c \"\$(curl -sSL https://is.gd/egitif || wget -qO- https://is.gd/egitif)\" -- $DOTFILES_OPTIONS'"
        )
    fi

    # Handle SSH service based on init system
    case "$DISTRO" in
    alpine)
        # SSH service is already handled in the Alpine-specific section above
        ;;
    *)
        runcmd+=(
            "systemctl enable --now sshd"
        )
        ;;
    esac

    # Create Alpine-specific cloud-init configuration
    if [[ "$DISTRO" == "alpine" ]]; then
        # Create package installation command for Alpine
        local package_install_cmd="apk add"
        for pkg in "${packages[@]}"; do
            package_install_cmd+=" $pkg"
        done

        sudo tee "${CLOUD_INIT_DIR}/user-data" >/dev/null <<EOF
#cloud-config
hostname: $VM_NAME
manage_etc_hosts: true

# User configuration
users:
  - name: $USERNAME
    groups:
$(printf "      - %s\n" "${USER_GROUPS[@]}")
    shell: /bin/bash
    sudo: ['ALL=(ALL) NOPASSWD:ALL']
    lock_passwd: false
    passwd: "$PASSWORD_HASH"
    ssh_authorized_keys:
      - $pub_key

# Alpine-specific configuration
# Disable automatic package management since Alpine repos might be stale
package_update: false
package_upgrade: false

# Use runcmd for manual package management
runcmd:
  - apk update
  - $package_install_cmd
$(printf "  - %s\n" "${runcmd[@]}")

# Ensure SSH service is enabled
ssh_pwauth: true
disable_root: false

timezone: UTC

write_files:
  - path: /etc/motd
    content: |
      $motd_content
    append: false

final_message: "VM $VM_NAME setup complete!"
EOF
    else
        # Standard cloud-init configuration for other distributions
        sudo tee "${CLOUD_INIT_DIR}/user-data" >/dev/null <<EOF
#cloud-config
hostname: $VM_NAME
manage_etc_hosts: true

# User configuration
users:
  - name: $USERNAME
    groups:
$(printf "      - %s\n" "${USER_GROUPS[@]}")
    shell: /bin/bash
    sudo: ['ALL=(ALL) NOPASSWD:ALL']
    lock_passwd: false
    passwd: "$PASSWORD_HASH"
    ssh_authorized_keys:
      - $pub_key

# System configuration
package_update: true
package_upgrade: true

packages:
$(printf "  - %s\n" "${packages[@]}")

runcmd:
$(printf "  - %s\n" "${runcmd[@]}")

# Ensure SSH service is enabled
ssh_pwauth: true
disable_root: false

timezone: UTC

write_files:
  - path: /etc/motd
    content: |
      $motd_content
    append: false

final_message: "VM $VM_NAME setup complete!"
EOF
    fi

    sudo tee "${CLOUD_INIT_DIR}/meta-data" >/dev/null <<EOF
instance-id: ${VM_NAME}-$(date +%s)
local-hostname: $VM_NAME
EOF

    sudo tee "${CLOUD_INIT_DIR}/network-config" >/dev/null <<EOF
version: 2
ethernets:
  enp1s0:
    dhcp4: true
EOF

    if [[ ! -f "${CLOUD_INIT_DIR}/user-data" ||
        ! -f "${CLOUD_INIT_DIR}/meta-data" ||
        ! -f "${CLOUD_INIT_DIR}/network-config" ]]; then
        fail "Failed to create one or more cloud-init configuration files"
        exit 1
    fi

    sudo chown -R "$QEMU_USER:$QEMU_GROUP" "$CLOUD_INIT_DIR"
    sudo chmod -R 644 "$CLOUD_INIT_DIR"/*
    sudo chmod 755 "$CLOUD_INIT_DIR"

    success "Cloud-init configuration generated"
}

create_cloud_init_iso() {
    slog "Creating cloud-init ISO..."

    if ! sudo mkisofs -output "$SEED_ISO" -volid cidata -joliet -rock \
        "${CLOUD_INIT_DIR}/user-data" \
        "${CLOUD_INIT_DIR}/meta-data" \
        "${CLOUD_INIT_DIR}/network-config" 2>/dev/null; then
        fail "Failed to create cloud-init ISO"
        exit 1
    fi

    if [[ ! -f "$SEED_ISO" ]]; then
        fail "Cloud-init ISO was not created: $SEED_ISO"
        exit 1
    fi

    sudo chown "$QEMU_USER:$QEMU_GROUP" "$SEED_ISO"
    sudo chmod 644 "$SEED_ISO"
    success "Cloud-init ISO created: $SEED_ISO"
}

launch_vm() {
    slog "Launching VM '$VM_NAME'..."

    local network_config
    if [[ "$BRIDGE_IF" == "default" ]]; then
        network_config="network=default,model=virtio"
    else
        network_config="bridge=$BRIDGE_IF,model=virtio"
    fi

    slog "VM Configuration:"
    slog "  Name: $VM_NAME"
    slog "  Distribution: $DISTRO $RELEASE"
    slog "  Memory: ${RAM_MB}MB"
    slog "  vCPUs: $VCPUS"
    slog "  Disk: $DISK_IMG ($DISK_SIZE)"
    slog "  Network: $network_config"
    slog "  OS Variant: $OS_VARIANT"

    if ! virt-install \
        --connect qemu:///system \
        --name "$VM_NAME" \
        --memory "$RAM_MB" \
        --vcpus "$VCPUS" \
        --disk path="$DISK_IMG",format=qcow2,bus=virtio \
        --disk path="$SEED_ISO",device=cdrom \
        --os-variant "$OS_VARIANT" \
        --virt-type kvm \
        --graphics none \
        --network "$network_config" \
        --import \
        --noautoconsole; then
        fail "Failed to create VM"
        exit 1
    fi

    if ! virsh list --all | grep -q "$VM_NAME"; then
        fail "VM '$VM_NAME' was not created successfully"
        exit 1
    fi

    success "VM '$VM_NAME' created successfully!"

    slog "Adding VM to /etc/hosts for name-based access..."
    sleep 5
    if [[ -x "$HOME/.ilm/bin/vm-hosts" ]]; then
        "$HOME/.ilm/bin/vm-hosts" add "$VM_NAME" || warn "Could not add VM to /etc/hosts automatically"
    elif [[ -x "$(has_cmd vm-hosts)" ]]; then
        vm-hosts add "$VM_NAME" || warn "Could not add VM to /etc/hosts automatically"
    else
        warn "vm-hosts script not found, skipping /etc/hosts update"
    fi
}

show_completion_info() {
    success "=== VM Creation Complete ==="
    echo
    slog "VM Details:"
    echo "  Name: $VM_NAME"
    echo "  Distribution: $DISTRO $RELEASE"
    echo "  Memory: ${RAM_MB}MB"
    echo "  vCPUs: $VCPUS"
    echo "  Disk: $DISK_SIZE"
    echo "  Username: $USERNAME"
    echo "  SSH Key: $SSH_KEY"
    echo

    if [[ "$INSTALL_DOCKER" == "true" ]]; then
        slog "Docker is pre-installed on this VM"
        echo "  Docker Commands:"
        echo "    Test Docker:        docker run hello-world"
        echo "    Check version:      docker --version"
        echo "    Check compose:      docker compose version"
        echo
    fi

    if [[ "$INSTALL_BREW" == "true" ]]; then
        slog "Homebrew is pre-installed on this VM"
        echo "  Homebrew Commands:"
        echo "    Check version:      brew --version"
        echo "    List packages:      brew list"
        echo "    Search packages:    brew search <term>"
        echo "    Install package:    brew install <package>"
        echo "    Update packages:    brew update && brew upgrade"
        echo "  Pre-installed tools: git, curl, wget, vim, htop, starship, fzf, ripgrep, eza, zoxide, fd, bat, gum, tmux, lazygit, git-delta, just, jq, yq"
        echo
    fi

    if [[ -n "$DOTFILES_OPTIONS" ]]; then
        slog "Dotfiles are pre-installed on this VM with options: $DOTFILES_OPTIONS"
        echo "  Dotfiles Commands:"
        echo "    Location:           ~/.ilm"
        echo "    Install shell:      ilmi shell"
        echo "    Install Docker:     ilmi docker"
        echo "    Install Python:     ilmi python"
        echo "    Config zsh:         ilmc zsh"
        echo "    Config tmux:        ilmc tmux"
        echo "    Config nvim:        ilmc nvim"
        echo "  Repository: https://github.com/pervezfunctor/dotfiles"
        echo
    fi

    slog "Useful Commands:"
    echo "  Check VM status:    virsh list --all"
    echo "  Start VM:           virsh start $VM_NAME"
    echo "  Stop VM:            virsh shutdown $VM_NAME"
    echo "  Force stop VM:      virsh destroy $VM_NAME"
    echo "  Delete VM:          virsh undefine $VM_NAME"
    echo "  Console access:     virsh console $VM_NAME"
    echo "  Get VM IP:          virsh domifaddr $VM_NAME"
    echo
    slog "SSH Access:"
    echo "  Wait 2-3 minutes for cloud-init to complete"
    echo "  Find VM IP:         virsh domifaddr $VM_NAME"
    echo "  SSH by name:        ssh $USERNAME@$VM_NAME"
    echo "  SSH by IP:          ssh $USERNAME@<VM_IP>"
    echo

    case "$DISTRO" in
    ubuntu | debian)
        slog "Package Management:"
        echo "  Update system:      sudo apt update && sudo apt upgrade"
        echo "  Install packages:   sudo apt install <package>"
        echo "  Search packages:    apt search <term>"
        ;;
    fedora)
        slog "Fedora Commands:"
        echo "  Update system:      sudo dnf update"
        echo "  Install packages:   sudo dnf install <package>"
        echo "  Search packages:    dnf search <term>"
        echo "  List installed:     dnf list installed"
        ;;
    arch)
        slog "Arch Linux Commands:"
        echo "  Update system:      sudo pacman -Syu"
        echo "  Install packages:   sudo pacman -S <package>"
        echo "  Search packages:    pacman -Ss <term>"
        echo "  List installed:     pacman -Q"
        echo "  AUR helper setup:   Install yay or paru for AUR access"
        ;;
    alpine)
        slog "Alpine Linux Commands:"
        echo "  Update system:      sudo apk update && sudo apk upgrade"
        echo "  Install packages:   sudo apk add <package>"
        echo "  Search packages:    apk search <term>"
        echo "  List installed:     apk info"
        echo "  Service management: rc-service <service> start/stop/restart"
        echo "  Enable service:     rc-update add <service> default"
        ;;
    esac

    echo
    warn "Note: Cloud-init setup takes 1-3 minutes."
}

echo_all_vars() {
    slog "All variables:"
    echo "  VM_NAME: $VM_NAME"
    echo "  DISTRO: $DISTRO"
    echo "  RELEASE: $RELEASE"
    echo "  USERNAME: $USERNAME"
    echo "  PASSWORD: $PASSWORD"
    echo "  SSH_KEY: $SSH_KEY"
    echo "  RAM_MB: $RAM_MB"
    echo "  VCPUS: $VCPUS"
    echo "  DISK_SIZE: $DISK_SIZE"
    echo "  BRIDGE_IF: $BRIDGE_IF"
    echo "  INSTALL_DOCKER: $INSTALL_DOCKER"
    echo "  INSTALL_BREW: $INSTALL_BREW"
    echo "  DOTFILES_OPTIONS: $DOTFILES_OPTIONS"
    echo "  OS_VARIANT: $OS_VARIANT"
    echo "  BASE_IMG_NAME: $BASE_IMG_NAME"
    echo "  BASE_IMG: $BASE_IMG"
    echo "  PASSWORD_HASH: $PASSWORD_HASH"
    echo "  WORKDIR: $WORKDIR"
    echo "  CLOUD_INIT_DIR: $CLOUD_INIT_DIR"
    echo "  DISK_IMG: $DISK_IMG"
    echo "  SEED_ISO: $SEED_ISO"

    echo "  Check the above variables for any issues before proceeding."
    echo "  Press enter to continue..."
    # read -r
}

main() {
    configure_distribution

    slog "Starting $DISTRO VM creation..."
    slog "VM Name: $VM_NAME"
    slog "Distribution: $DISTRO $RELEASE"
    slog "Working Directory: $WORKDIR"

    check_prerequisites
    start_sudo_keepalive
    echo_all_vars
    download_image
    create_vm_disk
    generate_cloud_init
    create_cloud_init_iso
    launch_vm
    show_completion_info
    stop_sudo_keepalive

    success "All done! Your $DISTRO VM is ready."
}

main "$@"
