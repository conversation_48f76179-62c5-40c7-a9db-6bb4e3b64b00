#!/bin/bash

set -euo pipefail

DOT_DIR=${DOT_DIR:-$HOME/.ilm}

# shellcheck disable=SC1091
source "$DOT_DIR/share/utils"

main() {

    has_cmd incus || fail "Incus is not installed. Please install it first."

    slog "Creating 6 Incus LXC containers (Ubuntu, Fedora, Arch, Debian, Alpine, NixOS)..."

    slog "Creating Ubuntu container: ubuntu"
    ict-create --distro ubuntu --name ubuntu

    slog "Creating Fedora container: fedora"
    ict-create --distro fedora --name fedora

    slog "Creating Arch container: arch"
    ict-create --distro arch --name arch

    slog "Creating Debian container: debian"
    ict-create --distro debian --name debian

    slog "Creating Alpine container: alpine"
    ict-create --distro alpine --name alpine

    slog "Creating NixOS container: nixos"
    ict-create --distro nixos --name nixos

    slog "Listing created containers:"
    incus list type=container

    success "All containers created successfully!"
    slog "You can access them using: incus exec <container-name> -- bash"
}

main "$@"
