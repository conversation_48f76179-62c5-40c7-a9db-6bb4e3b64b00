#!/bin/bash

set -euo pipefail

DOT_DIR=${DOT_DIR:-$HOME/.ilm}

# shellcheck disable=SC1091
source "$DOT_DIR/share/utils"

main() {

    has_cmd virt-install || fail "virt-install is not installed. Please install libvirt and virt-install first. You can use: vm install"

    slog "Creating 4 libvirt VMs (Ubuntu, Fedora, Arch, Debian)..."

    slog "Creating Ubuntu VM: dev"
    vm-create --distro ubuntu --name dev --release noble --docker --brew --dotfiles min

    slog "Creating Fedora VM: min"
    vm-create --distro fedora --name min --dotfiles min

    slog "Creating Arch VM: incus"
    vm-create --distro alpine --name incus --dotfiles min

    slog "Creating Debian VM: nix"
    vm-create --distro debian --name nix --nix

    slog "Listing created VMs:"
    virsh list --all

    success "All VMs created successfully!"
    slog "You can access them using: virsh console <vm-name>"
}

main "$@"
